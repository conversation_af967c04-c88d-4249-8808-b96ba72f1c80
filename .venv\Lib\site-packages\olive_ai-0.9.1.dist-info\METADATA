Metadata-Version: 2.4
Name: olive-ai
Version: 0.9.1
Summary: Olive: Simplify ML Model Finetuning, Conversion, Quantization, and Optimization for CPUs, GPUs and NPUs.
Home-page: https://microsoft.github.io/Olive/
Download-URL: https://github.com/microsoft/Olive/tags
Author: Microsoft Corporation
Author-email: <EMAIL>
License: MIT License
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft :: Windows
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.9
License-File: LICENSE
License-File: NOTICE.txt
Requires-Dist: numpy
Requires-Dist: onnx
Requires-Dist: onnxscript>=0.2.5
Requires-Dist: optuna
Requires-Dist: pandas
Requires-Dist: pydantic
Requires-Dist: pyyaml
Requires-Dist: torch
Requires-Dist: torchmetrics>=1.0.0
Requires-Dist: transformers
Provides-Extra: auto-opt
Requires-Dist: optimum; extra == "auto-opt"
Provides-Extra: azureml
Requires-Dist: azure-ai-ml>=1.11.1; extra == "azureml"
Requires-Dist: azure-keyvault-secrets; extra == "azureml"
Requires-Dist: azure-identity; extra == "azureml"
Requires-Dist: azureml-fsspec; extra == "azureml"
Provides-Extra: bnb
Requires-Dist: bitsandbytes; extra == "bnb"
Requires-Dist: triton; extra == "bnb"
Provides-Extra: capture-onnx-graph
Requires-Dist: optimum; extra == "capture-onnx-graph"
Provides-Extra: cpu
Requires-Dist: onnxruntime; extra == "cpu"
Provides-Extra: directml
Requires-Dist: onnxruntime-directml; extra == "directml"
Provides-Extra: docker
Requires-Dist: docker; extra == "docker"
Provides-Extra: shared-cache
Requires-Dist: azure-identity; extra == "shared-cache"
Requires-Dist: azure-storage-blob; extra == "shared-cache"
Provides-Extra: finetune
Requires-Dist: optimum; extra == "finetune"
Requires-Dist: accelerate>=0.30.0; extra == "finetune"
Requires-Dist: peft; extra == "finetune"
Requires-Dist: scipy; extra == "finetune"
Requires-Dist: bitsandbytes; extra == "finetune"
Requires-Dist: triton; extra == "finetune"
Provides-Extra: flash-attn
Requires-Dist: flash_attn; extra == "flash-attn"
Provides-Extra: gpu
Requires-Dist: onnxruntime-gpu; extra == "gpu"
Provides-Extra: inc
Requires-Dist: neural-compressor; extra == "inc"
Provides-Extra: lora
Requires-Dist: accelerate>=0.30.0; extra == "lora"
Requires-Dist: peft; extra == "lora"
Requires-Dist: scipy; extra == "lora"
Provides-Extra: nvmo
Requires-Dist: nvidia-modelopt; extra == "nvmo"
Requires-Dist: onnx-graphsurgeon; extra == "nvmo"
Requires-Dist: datasets>=2.14.4; extra == "nvmo"
Requires-Dist: cppimport==22.8.2; extra == "nvmo"
Provides-Extra: openvino
Requires-Dist: openvino>=2025.1.0; extra == "openvino"
Requires-Dist: nncf>=2.16.0; extra == "openvino"
Requires-Dist: numpy<2.0; extra == "openvino"
Requires-Dist: optimum[openvino]<=1.24; extra == "openvino"
Provides-Extra: optimum
Requires-Dist: optimum; extra == "optimum"
Provides-Extra: qnn
Requires-Dist: onnxruntime-qnn; extra == "qnn"
Provides-Extra: tf
Requires-Dist: tensorflow==1.15.0; extra == "tf"
Provides-Extra: torch-tensorrt
Requires-Dist: torch-tensorrt; extra == "torch-tensorrt"
Provides-Extra: tune-session-params
Requires-Dist: psutil; extra == "tune-session-params"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: download-url
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

Olive: Simplify ML Model Finetuning, Conversion, Quantization, and Optimization for CPUs, GPUs and NPUs
